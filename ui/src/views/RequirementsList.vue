<template>
  <div class="requirements-container">
    <!-- 页面标题 -->
    <div class="stats-header">
      <h2 class="stats-title">需求列表</h2>
      <el-button type="primary" @click="openBugReportDialog">
        <el-icon class="el-icon--left"><Plus /></el-icon>
        上报漏洞
      </el-button>
    </div>

    <!-- 统计信息工具栏 -->
    <div class="toolbar">
      <div class="toolbar-left">
        <div class="stats-info">
          共 <span class="stats-number">{{ totalItems }}</span> 条需求
        </div>
      </div>
    </div>

    <!-- 搜索筛选区域 -->
    <div class="page-section-title">筛选条件</div>
    <div class="filter-card">
      <el-form :inline="true" :model="searchForm" class="filter-form">
        <div class="filter-group">
          <label class="filter-label">需求类型</label>
          <el-select v-model="searchForm.type" placeholder="全部类型" clearable class="filter-control">
            <el-option v-for="(value, key) in requirementTypeOptions" :key="key" :label="value" :value="key"></el-option>
          </el-select>
        </div>
        <div class="filter-group">
          <label class="filter-label">优先级</label>
          <el-select v-model="searchForm.priority" placeholder="全部优先级" clearable class="filter-control">
            <el-option v-for="(value, key) in priorityOptions" :key="key" :label="value" :value="key"></el-option>
          </el-select>
        </div>
        <div class="filter-group">
          <label class="filter-label">状态</label>
          <el-select v-model="searchForm.status" placeholder="全部状态" clearable class="filter-control">
            <el-option v-for="(value, key) in statusOptions" :key="key" :label="value" :value="key"></el-option>
          </el-select>
        </div>
        <div class="filter-group">
          <label class="filter-label">关键词</label>
          <el-input v-model="searchForm.keyword" placeholder="需求编号/名称" clearable class="filter-control"></el-input>
        </div>
        <div class="filter-actions">
          <el-button @click="resetSearch">重置</el-button>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
        </div>
      </el-form>
    </div>

    <div class="requirements-content">
      <div v-if="loading" class="loading-container">
        <el-loading :fullscreen="false" text="加载中..." />
      </div>

      <!-- 需求卡片网格 -->
      <div v-else class="requirements-grid">
        <requirement-card
          v-for="item in requirements"
          :key="item.requirementId"
          :requirement="item"
          :user-role="getUserRoleForRequirement(item)"
          :current-user-id="currentUserId"
          @view="viewRequirement"
          @edit="editRequirement"
          @publish="publishRequirement"
          @delete="deleteRequirement"
          @claim="startRequirement"
          @submitToTest="submitToTest"
          @withdrawFromTest="withdrawFromTest"
          @withdrawFromValidation="withdrawFromValidation"
          @approveTest="approveTest"
          @rejectTest="rejectTest"
          @approveValidation="approveValidation"
          @rejectValidation="rejectValidation"
        />

        <!-- 空状态 -->
        <div v-if="requirements.length === 0" class="empty-state">
          <div class="empty-icon">📋</div>
          <div class="empty-text">暂无需求</div>
          <div class="empty-desc">尝试调整筛选条件或添加新的需求</div>
        </div>
      </div>

      <!-- 分页组件 -->
      <div class="pagination-container" v-if="totalItems > 0">
        <div class="pagination-info">
          显示第 {{ (currentPage - 1) * pageSize + 1 }}-{{ Math.min(currentPage * pageSize, totalItems) }} 条，共 {{ totalItems }} 条记录
        </div>
        <div class="pagination-controls">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :page-sizes="[6, 12, 24, 48]"
            layout="prev, pager, next, sizes"
            :total="totalItems"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </div>

    <!-- 使用公共需求详情组件 -->
    <requirement-detail
      v-model:visible="showRequirementDetailDialog"
      :requirement-id="currentRequirementId"
      @closed="handleDetailDialogClosed"
    />

    <!-- 驳回理由对话框 -->
    <el-dialog
      v-model="showRejectReasonDialog"
      :title="rejectType === 'test' ? '驳回测试' : '驳回验证'"
      width="30%"
      :close-on-click-modal="false"
      :close-on-press-escape="true"
    >
      <el-form :model="rejectForm" label-width="80px">
        <el-form-item label="驳回理由" required>
          <el-input
            v-model="rejectForm.reason"
            type="textarea"
            :rows="4"
            placeholder="请输入驳回理由"
          ></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showRejectReasonDialog = false">取消</el-button>
          <el-button type="primary" @click="submitReject" :disabled="!rejectForm.reason">确定</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 上报漏洞对话框 -->
    <RequirementFormDialog
      v-model:visible="showBugReportDialog"
      mode="bug"
      :projects-list="projectsList"
      :project-developers="projectDevelopers"
      :project-branches="projectBranches"
      @submit="handleBugSubmit"
      @project-change="handleProjectChange"
    />

    <!-- 编辑需求对话框 -->
    <RequirementFormDialog
      v-model:visible="showEditRequirementDialog"
      mode="requirement"
      :projects-list="projectsList"
      :project-developers="projectDevelopers"
      :project-branches="projectBranches"
      :initial-data="currentEditRequirement"
      @submit="handleEditRequirementSubmit"
      @project-change="handleProjectChange"
    />
  </div>
</template>

<script>
import { ref, onMounted, computed } from 'vue';
import { Plus, Clock } from '@element-plus/icons-vue';
import {
  getRequirementsList,
  getProjects,
  getProjectDevelopers,
  getProjectDevBranches,
  createRequirement,
  updateRequirement,
  getRequirementDetail,
  publishRequirement as apiPublishRequirement,
  deleteRequirement as apiDeleteRequirement,
  claimRequirement as apiClaimRequirement,
  submitToTest as apiSubmitToTest,
  withdrawFromTest as apiWithdrawFromTest,
  withdrawFromValidation as apiWithdrawFromValidation,
  approveTest as apiApproveTest,
  rejectTest as apiRejectTest,
  approveValidation as apiApproveValidation,
  rejectValidation as apiRejectValidation
} from '../api/project';
import { formatToDate, formatToDateTime } from '../utils/dateUtils';
import { ElMessage, ElMessageBox } from 'element-plus';
import { marked } from 'marked';
import 'mavon-editor/dist/css/index.css';
import RequirementDetail from '../components/RequirementDetail.vue';
import RequirementCard from '../components/RequirementCard.vue';
import RequirementFormDialog from '../components/RequirementFormDialog.vue';

export default {
  name: 'RequirementsList',
  components: {
    RequirementDetail,
    RequirementCard,
    RequirementFormDialog,
    Plus,
    Clock
  },
  setup() {
    const requirements = ref([]);
    const showRequirementDetailDialog = ref(false);
    const showBugReportDialog = ref(false);
    const showRejectReasonDialog = ref(false);
    const currentRequirementId = ref(null);
    const loading = ref(false);
    const submitting = ref(false);
    const currentViewingItem = ref(null); // 当前正在查看的需求项
    const rejectType = ref(''); // 'test' 或 'validation'
    const currentRejectItem = ref(null); // 当前要驳回的项目
    const rejectForm = ref({ reason: '' }); // 驳回理由表单

    // 分页相关
    const currentPage = ref(1);
    const pageSize = ref(12);
    const totalItems = ref(0);

    // 判断当前用户是否为管理员
    const isAdmin = computed(() => {
      const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}');
      return userInfo.is_admin === true;
    });

    // 用户角色和ID
    const currentUserId = computed(() => {
      const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}');
      return userInfo.id;
    });

    // 动态判断用户角色的函数
    const getUserRoleForRequirement = (requirement) => {
      const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}');
      if (userInfo.is_admin === true) return 'admin';

      const userId = currentUserId.value;
      if (!userId) return 'developer'; // 默认角色

      // 检查用户是否是该需求的开发人员
      if (requirement.developers && Array.isArray(requirement.developers)) {
        const isDeveloper = requirement.developers.some(dev =>
          (typeof dev === 'object' ? dev.id : dev) == userId
        );
        if (isDeveloper) return 'developer';
      }

      // 检查用户是否是该需求的测试人员
      if (requirement.testers && Array.isArray(requirement.testers)) {
        const isTester = requirement.testers.some(tester =>
          (typeof tester === 'object' ? tester.id : tester) == userId
        );
        if (isTester) return 'tester';
      }

      // 如果用户既不是开发人员也不是测试人员，默认为开发人员
      return 'developer';
    };

    // 为了兼容现有代码，保留一个通用的userRole
    const userRole = computed(() => {
      const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}');
      if (userInfo.is_admin === true) return 'admin';
      return 'developer'; // 默认角色，具体角色通过getUserRoleForRequirement动态判断
    });

    // 搜索表单
    const searchForm = ref({
      type: '',
      priority: '',
      status: '',
      keyword: ''
    });

    // 项目列表
    const projectsList = ref([]);
    // 项目开发人员
    const projectDevelopers = ref([]);
    // 项目分支
    const projectBranches = ref([]);

    // 需求类型映射
    const requirementTypes = {
      'Bug Fixed': '修复漏洞',
      'Hot Bug Fixed': '紧急修复漏洞',
      'New Feature': '新功能'
    };

    // 需求类型选项
    const requirementTypeOptions = {
      'Bug Fixed': '修复漏洞',
      'Hot Bug Fixed': '紧急修复漏洞',
      'New Feature': '新功能'
    };

    // 优先级选项
    const priorityOptions = {
      'P0': 'P0',
      'P1': 'P1',
      'P2': 'P2'
    };

    // 状态选项
    const statusOptions = {
      'PENDING': '待处理',
      'DEVELOPING': '开发中',
      'TESTING': '测试中',
      'VALIDATING': '验证中',
      'COMPLETED': '已完成'
    };



    // 获取历史记录项的类型
    const getHistoryItemType = (action) => {
      switch (action) {
        case '提交需求': return 'primary';
        case '认领需求': return 'success';
        case '提交测试': return 'warning';
        case '撤回测试': return 'info';
        case '通过测试': return 'success';
        case '驳回测试': return 'danger';
        case '撤回验证': return 'info';
        default: return 'info';
      }
    };

    // 渲染Markdown内容
    const renderMarkdown = (content) => {
      if (!content) return '';
      return marked(content);
    };

    // 复制到剪贴板
    const copyToClipboard = (text) => {
      navigator.clipboard.writeText(text).then(() => {
        ElMessage.success('已复制到剪贴板');
      }).catch(() => {
        ElMessage.error('复制失败');
      });
    };

    // 搜索处理
    const handleSearch = () => {
      currentPage.value = 1; // 重置为第一页
      fetchRequirements();
    };

    // 重置搜索
    const resetSearch = () => {
      searchForm.value = {
        type: '',
        priority: '',
        status: '',
        keyword: ''
      };
      currentPage.value = 1; // 重置为第一页
      fetchRequirements();
    };

    // 开始处理需求（认领）
    const startRequirement = async (item) => {
      try {
        // 确认对话框
        await ElMessageBox.confirm('确定要认领该需求吗?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'info'
        });

        // 调用认领API
        await apiClaimRequirement(item.requirementId);
        ElMessage.success('需求已认领，状态已更新为开发中');

        // 刷新列表
        fetchRequirements();
      } catch (error) {
        if (error !== 'cancel') {
          console.error('认领需求失败:', error);
          ElMessage.error('认领需求失败: ' + (error.message || '未知错误'));
        }
      }
    };

    // 提交测试
    const submitToTest = async (item) => {
      try {
        // 确认对话框
        await ElMessageBox.confirm('确定要将该需求提交测试吗?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'info'
        });

        // 调用提交测试API
        await apiSubmitToTest(item.requirementId);
        ElMessage.success('需求状态已更新为测试中');

        // 刷新列表
        fetchRequirements();
      } catch (error) {
        if (error !== 'cancel') {
          console.error('提交测试失败:', error);
          ElMessage.error('提交测试失败: ' + (error.message || '未知错误'));
        }
      }
    };

    // 撤回测试
    const withdrawFromTest = async (item) => {
      try {
        // 确认对话框
        await ElMessageBox.confirm('确定要撤回测试吗?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'info'
        });

        // 调用撤回测试API
        await apiWithdrawFromTest(item.requirementId);
        ElMessage.success('需求已撤回，状态已更新为开发中');

        // 刷新列表
        fetchRequirements();
      } catch (error) {
        if (error !== 'cancel') {
          console.error('撤回测试失败:', error);
          ElMessage.error('撤回测试失败: ' + (error.message || '未知错误'));
        }
      }
    };

    // 撤回验证
    const withdrawFromValidation = async (item) => {
      try {
        // 确认对话框
        await ElMessageBox.confirm('确定要撤回验证吗?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'info'
        });

        // 调用撤回验证API
        await apiWithdrawFromValidation(item.requirementId);
        ElMessage.success('需求验证已撤回');

        // 刷新列表
        fetchRequirements();
      } catch (error) {
        if (error !== 'cancel') {
          console.error('撤回验证失败:', error);
          ElMessage.error('撤回验证失败: ' + (error.message || '未知错误'));
        }
      }
    };

    // 测试通过
    const approveTest = async (item) => {
      try {
        // 确认对话框
        await ElMessageBox.confirm('确定要通过测试吗?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'info'
        });

        // 调用通过测试API
        await apiApproveTest(item.requirementId);
        ElMessage.success('需求状态已更新为验证中');

        // 刷新列表
        fetchRequirements();
      } catch (error) {
        if (error !== 'cancel') {
          console.error('通过测试失败:', error);
          ElMessage.error('通过测试失败: ' + (error.message || '未知错误'));
        }
      }
    };

    // 显示驳回对话框
    const showRejectDialog = (item, type) => {
      rejectType.value = type;
      currentRejectItem.value = item;
      rejectForm.value.reason = ''; // 清空理由
      showRejectReasonDialog.value = true;
    };

    // 提交驳回
    const submitReject = async () => {
      try {
        if (!rejectForm.value.reason) {
          ElMessage.warning('请输入驳回理由');
          return;
        }

        if (rejectType.value === 'test') {
          // 驳回测试
          await apiRejectTest(currentRejectItem.value.requirementId, rejectForm.value.reason);
          ElMessage.success('需求已驳回，状态已更新为开发中');
        } else if (rejectType.value === 'validation') {
          // 驳回验证
          await apiRejectValidation(currentRejectItem.value.requirementId, rejectForm.value.reason);
          ElMessage.success('需求验证已驳回，状态已更新为开发中');
        }

        // 关闭对话框
        showRejectReasonDialog.value = false;

        // 刷新列表
        fetchRequirements();
      } catch (error) {
        console.error('驳回操作失败:', error);
        ElMessage.error('驳回操作失败: ' + (error.message || '未知错误'));
      }
    };

    // 管理员通过验证
    const approveValidation = async (item) => {
      try {
        // 确认对话框
        await ElMessageBox.confirm('确定要通过验证吗? 通过后需求将标记为已完成。', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'info'
        });

        // 调用通过验证API
        await apiApproveValidation(item.requirementId);
        ElMessage.success('需求验证已通过，状态已更新为已完成');

        // 刷新列表
        fetchRequirements();
      } catch (error) {
        if (error !== 'cancel') {
          console.error('通过验证失败:', error);
          ElMessage.error('通过验证失败: ' + (error.message || '未知错误'));
        }
      }
    };

    // 将状态代码转换为中文显示
    const getStatusCodeByDisplay = (displayName) => {
      for (const [code, display] of Object.entries(statusOptions)) {
        if (display === displayName) {
          return code;
        }
      }
      return null;
    };

    // 处理页码变化
    const handleCurrentChange = (page) => {
      currentPage.value = page;
      fetchRequirements();
    };

    // 处理每页条数变化
    const handleSizeChange = (size) => {
      pageSize.value = size;
      currentPage.value = 1; // 重置为第一页
      fetchRequirements();
    };

    const fetchRequirements = async () => {
      try {
        loading.value = true;

        // 构建查询参数
        const params = {
          page: currentPage.value,
          pageSize: pageSize.value
        };

        if (searchForm.value.type) params.type = searchForm.value.type;
        if (searchForm.value.priority) params.priority = searchForm.value.priority;
        if (searchForm.value.status) params.status = searchForm.value.status;
        if (searchForm.value.keyword) params.keyword = searchForm.value.keyword;

        console.log('搜索参数:', params);

        // 使用getRequirementsList API，支持筛选和分页
        const response = await getRequirementsList(params);
        console.log('需求列表响应:', response);

        // 获取当前用户ID
        const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}');
        const currentUserId = userInfo.id;

        // 处理响应数据
        let processedRequirements = [];
        let total = 0;

        if (Array.isArray(response)) {
          // 如果返回的是数组，则没有分页信息
          processedRequirements = response;
          total = response.length;
        } else if (response && response.items) {
          // 如果返回的是分页对象
          processedRequirements = response.items;
          total = response.total;

          // 确保页码和每页条数与服务器返回的一致
          if (response.page) currentPage.value = response.page;
          if (response.pageSize) pageSize.value = response.pageSize;
        } else if (response) {
          // 兼容其他可能的返回格式
          processedRequirements = response;
          total = processedRequirements.length;
        }

        // 更新总条数
        totalItems.value = total;

        // 处理每个需求项
        const processed = (processedRequirements || []).map(item => {
          // 处理assignments字段：分隔逗号，取得数组，并去重
          let assignments = [];
          if (item.assignments && typeof item.assignments === 'string') {
            assignments = [...new Set(item.assignments.split(','))];
          } else if (Array.isArray(item.assignments)) {
            assignments = [...new Set(item.assignments)];
          }

          // 处理testers字段：分隔逗号，取得数组，并去重
          let testers = [];
          if (item.testers && typeof item.testers === 'string') {
            testers = [...new Set(item.testers.split(','))];
          } else if (Array.isArray(item.testers)) {
            testers = [...new Set(item.testers)];
          }

          // 检查当前用户是否为开发人员或测试人员
          const isDeveloper = assignments.some(id => Number(id) === currentUserId);
          const isTester = testers.some(id => Number(id) === currentUserId);

          // 转换需求类型为中文
          const typeInChinese = requirementTypes[item.type] || item.type;

          return {
            ...item,
            assignments,
            testers,
            isDeveloper,
            isTester,
            typeDisplay: typeInChinese
          };
        });

        requirements.value = processed;
      } catch (error) {
        console.error('获取需求列表失败:', error);
        ElMessage.error('获取需求列表失败: ' + (error.message || '未知错误'));
      } finally {
        loading.value = false;
      }
    };

    // 查看需求详情
    const viewRequirement = (item) => {
      currentViewingItem.value = item; // 保存当前查看的需求项
      currentRequirementId.value = item.requirementId;
      showRequirementDetailDialog.value = true;
    };

    // 处理详情对话框关闭事件
    const handleDetailDialogClosed = () => {
      currentViewingItem.value = null;
    };



    // 获取项目列表
    const fetchProjects = async () => {
      try {
        const response = await getProjects();
        projectsList.value = response;
      } catch (error) {
        console.error('获取项目列表失败:', error);
        ElMessage.error('获取项目列表失败: ' + (error.message || '未知错误'));
      }
    };

    // 处理项目选择变更
    const handleProjectChange = async (projectId) => {
      console.log('项目选择变更:', projectId);

      if (!projectId) {
        projectDevelopers.value = [];
        projectBranches.value = [];
        return;
      }

      try {
        console.log('开始获取项目信息...');

        // 获取项目开发人员
        const developers = await getProjectDevelopers(projectId);
        console.log('获取到开发人员:', developers);
        projectDevelopers.value = developers;

        // 获取项目分支
        const branches = await getProjectDevBranches(projectId);
        console.log('获取到分支:', branches);
        projectBranches.value = branches;
      } catch (error) {
        console.error('获取项目信息失败:', error);
        ElMessage.error('获取项目信息失败: ' + (error.message || '未知错误'));
      }
    };

    // 打开上报漏洞对话框
    const openBugReportDialog = () => {
      showBugReportDialog.value = true;
      // 确保项目列表已加载
      if (projectsList.value.length === 0) {
        fetchProjects();
      }
    };

    // 处理漏洞提交
    const handleBugSubmit = async (formData) => {
      try {
        // 提交漏洞报告（使用创建需求的API）
        await createRequirement(formData);
        ElMessage.success('漏洞报告提交成功');

        // 刷新需求列表
        fetchRequirements();
      } catch (error) {
        console.error('提交漏洞报告失败:', error);
        ElMessage.error('提交漏洞报告失败: ' + (error.message || '未知错误'));
        throw error; // 重新抛出错误，让组件处理loading状态
      }
    };



    // 编辑需求状态
    const showEditRequirementDialog = ref(false);
    const currentEditRequirement = ref(null);

    // 新的需求操作事件处理方法
    const editRequirement = async (requirement) => {
      try {
        // 获取需求详情
        const response = await getRequirementDetail(requirement.requirementId);
        currentEditRequirement.value = response;

        // 确保项目数据已加载
        if (projectsList.value.length === 0) {
          await fetchProjects();
        }

        // 如果有项目ID，加载对应的开发人员和分支数据
        if (response.project_id) {
          await handleProjectChange(response.project_id);
        }

        showEditRequirementDialog.value = true;
      } catch (error) {
        console.error('获取需求详情失败:', error);
        ElMessage.error('获取需求详情失败: ' + (error.message || '未知错误'));
      }
    };

    // 处理编辑需求提交
    const handleEditRequirementSubmit = async (formData) => {
      try {
        // 添加需求ID到表单数据
        const updateData = {
          ...formData,
          id: currentEditRequirement.value.id
        };

        // 调用更新需求API
        await updateRequirement(updateData);
        ElMessage.success('需求更新成功');

        // 关闭对话框
        showEditRequirementDialog.value = false;
        currentEditRequirement.value = null;

        // 刷新需求列表
        fetchRequirements();
      } catch (error) {
        console.error('更新需求失败:', error);
        ElMessage.error('更新需求失败: ' + (error.message || '未知错误'));
        throw error; // 重新抛出错误，让组件处理loading状态
      }
    };

    const publishRequirement = async (requirement) => {
      try {
        // 确认对话框
        await ElMessageBox.confirm('确定要发布该草稿需求吗？发布后将创建GitLab分支并通知相关人员。', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'info'
        });

        // 调用发布需求API
        await apiPublishRequirement(requirement.requirementId);
        ElMessage.success('需求已发布，状态已更新为待认领');

        // 刷新列表
        fetchRequirements();
      } catch (error) {
        if (error !== 'cancel') {
          console.error('发布需求失败:', error);
          ElMessage.error('发布需求失败: ' + (error.message || '未知错误'));
        }
      }
    };

    const deleteRequirement = async (requirement) => {
      try {
        // 确认对话框
        await ElMessageBox.confirm(
          `确定要删除需求"${requirement.requirementName}"吗？此操作将同时删除相关的GitLab分支，且无法恢复。`,
          '危险操作',
          {
            confirmButtonText: '确定删除',
            cancelButtonText: '取消',
            type: 'error',
            confirmButtonClass: 'el-button--danger'
          }
        );

        // 调用删除需求API
        await apiDeleteRequirement(requirement.requirementId);
        ElMessage.success('需求已删除');

        // 刷新列表
        fetchRequirements();
      } catch (error) {
        if (error !== 'cancel') {
          console.error('删除需求失败:', error);
          ElMessage.error('删除需求失败: ' + (error.message || '未知错误'));
        }
      }
    };

    const rejectTest = (requirement) => {
      showRejectDialog(requirement, 'test');
    };

    const rejectValidation = (requirement) => {
      showRejectDialog(requirement, 'validation');
    };

    onMounted(() => {
      fetchRequirements();
      fetchProjects();
    });

    return {
      requirements,
      formatToDate,
      formatToDateTime,
      viewRequirement,
      requirementTypes,
      requirementTypeOptions,
      priorityOptions,
      statusOptions,
      getHistoryItemType,
      getStatusCodeByDisplay,
      renderMarkdown,
      showRequirementDetailDialog,
      showBugReportDialog,
      showRejectReasonDialog,
      currentRequirementId,
      loading,
      submitting,
      copyToClipboard,
      currentViewingItem,
      searchForm,
      handleSearch,
      resetSearch,
      handleDetailDialogClosed,

      // 分页相关
      currentPage,
      pageSize,
      totalItems,
      handleCurrentChange,
      handleSizeChange,

      // 操作相关
      startRequirement,
      submitToTest,
      withdrawFromTest,
      withdrawFromValidation,
      approveTest,
      showRejectDialog,
      submitReject,
      approveValidation,
      editRequirement,
      publishRequirement,
      deleteRequirement,
      rejectTest,
      rejectValidation,
      isAdmin,
      userRole,
      currentUserId,
      getUserRoleForRequirement,
      rejectType,
      currentRejectItem,
      rejectForm,

      // 漏洞报告相关
      projectsList,
      projectDevelopers,
      projectBranches,
      openBugReportDialog,
      handleProjectChange,
      handleBugSubmit,

      // 编辑需求相关
      showEditRequirementDialog,
      currentEditRequirement,
      handleEditRequirementSubmit
    };
  }
};
</script>

<style scoped>
/* 基础样式 - 与Home.vue保持一致 */
.requirements-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  font-family: "Microsoft YaHei", -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Arial, sans-serif;
  min-height: 100vh;
}

/* 页面标题 - 与Home.vue保持一致 */
.stats-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.stats-title {
  font-size: 18px;
  font-weight: 500;
  color: var(--text-primary);
  margin: 0;
}

/* 区块标题 - 与Home.vue保持一致 */
.page-section-title {
  font-size: 20px;
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: var(--spacing-medium);
  padding-bottom: var(--spacing-small);
  border-bottom: 1px solid #eee;
}

/* 工具栏样式 */
.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-large);
}

.toolbar-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.stats-info {
  font-size: 15px;
  color: var(--text-secondary);
}

.stats-number {
  color: var(--primary-color);
  font-weight: 500;
}

/* 搜索筛选区域 */
.filter-card {
  background-color: #fff;
  border-radius: var(--radius-card);
  box-shadow: var(--shadow-md);
  padding: var(--spacing-large);
  margin-bottom: var(--spacing-large);
}

.filter-form {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  align-items: end;
}

.filter-group {
  display: flex;
  flex-direction: column;
}

.filter-label {
  font-size: 14px;
  color: var(--text-secondary);
  margin-bottom: 8px;
}

.filter-control {
  width: 160px;
}

/* 统一表单组件高度 */
.filter-control :deep(.el-input__wrapper) {
  height: 40px;
  box-sizing: border-box;
}

.filter-control :deep(.el-select .el-input__wrapper) {
  height: 40px;
  box-sizing: border-box;
}

.filter-control :deep(.el-input__inner) {
  height: 38px;
  line-height: 38px;
}

.filter-control :deep(.el-select__wrapper) {
  height: 40px;
  box-sizing: border-box;
}

.filter-actions {
  display: flex;
  gap: 12px;
}

.filter-actions .el-button {
  height: 40px;
  min-width: 80px;
}

/* 内容区域 */
.requirements-content {
  flex: 1;
  background-color: transparent;
  overflow: visible;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300px;
  background-color: #fff;
  border-radius: var(--radius-card);
  box-shadow: var(--shadow-md);
}

/* 需求卡片网格 */
.requirements-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(380px, 1fr));
  gap: var(--spacing-large);
  margin-bottom: var(--spacing-large);
}





/* 空状态 */
.empty-state {
  grid-column: 1 / -1;
  padding: 60px 0;
  text-align: center;
  color: var(--text-hint);
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.3;
}

.empty-text {
  font-size: 16px;
  margin-bottom: 8px;
}

.empty-desc {
  font-size: 14px;
  color: var(--text-hint);
}

/* 分页 */
.pagination-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #fff;
  border-radius: var(--radius-card);
  box-shadow: var(--shadow-md);
  padding: 20px 24px;
}

.pagination-info {
  font-size: 14px;
  color: var(--text-secondary);
}

.pagination-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 响应式设计 */
@media (max-width: 1366px) {
  .filter-form {
    gap: 12px;
  }
  .filter-control {
    width: 140px;
  }
  .requirements-grid {
    grid-template-columns: repeat(auto-fill, minmax(340px, 1fr));
    gap: 20px;
  }
}

@media (max-width: 1200px) {
  .filter-form {
    flex-direction: column;
    align-items: stretch;
  }
  .filter-group {
    width: 100%;
  }
  .filter-control {
    width: 100%;
  }
  .filter-actions {
    justify-content: flex-end;
  }
  .requirements-grid {
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 16px;
  }
}

@media (max-width: 768px) {
  .requirements-grid {
    grid-template-columns: 1fr;
  }
}

/* Markdown编辑器样式 */
:deep(.v-note-wrapper) {
  z-index: 1;
  width: 100% !important;
}

:deep(.v-note-panel) {
  min-height: 300px;
  width: 100% !important;
}

:deep(.v-note-edit) {
  min-height: 300px;
  width: 100% !important;
}

:deep(.v-note-edit .auto-textarea-input),
:deep(.v-note-edit textarea) {
  background-color: #FFF !important;
  width: 100% !important;
}

:deep(.v-note-edit) {
  background-color: #FFF !important;
}
</style>
