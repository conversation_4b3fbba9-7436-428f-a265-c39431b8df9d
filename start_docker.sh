#!/bin/bash

# 构建docker镜像
echo "构建 Docker 镜像..."
docker build -t twl-project-manage .

# 创建数据目录
echo "创建数据目录..."
mkdir -p docker-data

# 确保site.db文件存在
if [ ! -f "site.db" ]; then
    echo "创建空的site.db文件..."
    touch site.db
fi

# 运行docker容器
echo "启动 Docker 容器..."
docker run --name twl-project-manage \
  -p 20080:80 \
  -p 28000:8000 \
  -p 25173:5173 \
  -v $(pwd)/docker-data:/app/api/data \
  -v $(pwd)/site.db:/app/site.db \
  -d twl-project-manage

echo "容器已启动，等待5秒..."
sleep 5

# 检查容器是否运行
if [ "$(docker ps | grep twl-project-manage)" ]; then
    echo "容器运行成功！"
    echo "前端地址: http://localhost:80"
    echo "后端API地址: http://localhost:8000"

    # 显示容器日志
    echo "容器日志:"
    docker logs twl-project-manage

    echo "使用以下命令查看实时日志:"
    echo "docker logs -f twl-project-manage"

    echo "使用以下命令进入容器内部调试:"
    echo "docker exec -it twl-project-manage /bin/bash"
else
    echo "容器启动失败！查看详细错误日志:"
    docker logs twl-project-manage
fi