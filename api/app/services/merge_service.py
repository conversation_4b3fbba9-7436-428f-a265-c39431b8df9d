import logging
import threading
import time
from typing import List, Dict, Any
from sqlalchemy.orm import Session
from app.core.database import get_db
from app.models.requirement import (
    Requirement as RequirementModel,
    RequirementBranch,
    RequirementPullRequest,
    RequirementHistory,
    RequirementStatus,
    RequirementAction,
    PullRequestStatus,
    get_utc_now
)
from app.models.user import User
from app.models.project import Project, project_testers_table
from app.utils.gitlab_api import gitlab_api
from app.services.notification_service import NotificationService

logger = logging.getLogger(__name__)


class MergeService:
    """异步合并服务"""
    
    @staticmethod
    def start_async_merge(requirement_id: int, operator_user_id: int):
        """
        启动异步合并任务
        
        Args:
            requirement_id: 需求ID
            operator_user_id: 操作人用户ID
        """
        logger.info(f"[异步合并] 启动合并任务: requirement_id={requirement_id}, operator={operator_user_id}")
        
        # 创建后台线程执行合并任务
        merge_thread = threading.Thread(
            target=MergeService._execute_merge_task,
            args=(requirement_id, operator_user_id),
            daemon=True  # 设置为守护线程
        )
        merge_thread.start()
        
        logger.info(f"[异步合并] 合并任务已启动，线程ID: {merge_thread.ident}")
    
    @staticmethod
    def _execute_merge_task(requirement_id: int, operator_user_id: int):
        """
        执行合并任务的具体逻辑
        
        Args:
            requirement_id: 需求ID
            operator_user_id: 操作人用户ID
        """
        db = None
        try:
            # 获取数据库会话
            db = next(get_db())
            
            logger.info(f"[异步合并] 开始执行合并任务: requirement_id={requirement_id}")
            
            # 查询需求和相关信息
            db_requirement = db.query(RequirementModel).filter(RequirementModel.id == requirement_id).first()
            if not db_requirement:
                logger.error(f"[异步合并] 需求不存在: {requirement_id}")
                return
            
            operator = db.query(User).filter(User.id == operator_user_id).first()
            if not operator:
                logger.error(f"[异步合并] 操作人不存在: {operator_user_id}")
                return
            
            project = db.query(Project).filter(Project.id == db_requirement.project_id).first()
            if not project:
                logger.error(f"[异步合并] 项目不存在: {db_requirement.project_id}")
                return
            
            source_branch = db_requirement.git_branch
            if not source_branch:
                logger.error(f"[异步合并] 需求没有关联的源分支: {requirement_id}")
                MergeService._handle_merge_failure(db, db_requirement, operator, project, "需求没有关联的源分支")
                return
            
            branch_records = db.query(RequirementBranch).filter(RequirementBranch.requirement_id == requirement_id).all()
            if not branch_records:
                logger.error(f"[异步合并] 需求没有关联的目标分支: {requirement_id}")
                MergeService._handle_merge_failure(db, db_requirement, operator, project, "需求没有关联的目标分支")
                return
            
            # 执行合并流程
            MergeService._perform_merge_process(db, db_requirement, operator, project, source_branch, branch_records)
            
        except Exception as e:
            logger.error(f"[异步合并] 合并任务执行失败: {str(e)}", exc_info=True)
            if db and db_requirement:
                try:
                    MergeService._handle_merge_failure(db, db_requirement, operator, project, str(e))
                except Exception as cleanup_error:
                    logger.error(f"[异步合并] 清理失败状态时出错: {str(cleanup_error)}")
        finally:
            if db:
                db.close()
    
    @staticmethod
    def _perform_merge_process(db: Session, requirement: RequirementModel, operator: User, 
                             project: Project, source_branch: str, branch_records: List[RequirementBranch]):
        """
        执行完整的合并流程
        """
        created_prs = []
        merged_prs = []
        operation_results = []
        
        try:
            # 第一步：为每个目标分支创建合并请求
            logger.info(f"[异步合并] 开始创建PR: requirement_id={requirement.id}")
            for branch_record in branch_records:
                target_branch = branch_record.branch_name
                try:
                    title = f"Merge {source_branch} to {target_branch}"
                    description = f"需求: {requirement.title}\n异步自动合并操作"
                    
                    logger.info(f"[异步合并] 创建PR: {source_branch} → {target_branch}")
                    pr_result = gitlab_api.create_merge_request(
                        project_id=project.id,
                        source_branch=source_branch,
                        target_branch=target_branch,
                        title=title,
                        description=description
                    )
                    
                    pr_info = {
                        "target_branch": target_branch,
                        "pr_iid": pr_result.get("iid"),
                        "pr_url": pr_result.get("web_url"),
                        "pr_result": pr_result
                    }
                    created_prs.append(pr_info)
                    
                    operation_results.append({
                        "step": "create_pr",
                        "target_branch": target_branch,
                        "status": "success",
                        "pr_iid": pr_result.get("iid")
                    })
                    
                    logger.info(f"[异步合并] PR创建成功: {pr_result.get('web_url')}")
                    
                except Exception as e:
                    error_msg = f"创建PR失败 ({target_branch}): {str(e)}"
                    logger.error(f"[异步合并] {error_msg}")
                    operation_results.append({
                        "step": "create_pr",
                        "target_branch": target_branch,
                        "status": "error",
                        "error": error_msg
                    })
                    raise Exception(error_msg)
            
            # 第二步：等待并合并所有PR
            logger.info(f"[异步合并] 开始合并所有PR")
            for pr_info in created_prs:
                try:
                    target_branch = pr_info["target_branch"]
                    pr_iid = pr_info["pr_iid"]
                    
                    logger.info(f"[异步合并] 合并PR: IID={pr_iid}, 目标分支={target_branch}")
                    merge_result = gitlab_api.merge_pull_request(
                        project_id=project.id,
                        merge_request_iid=pr_iid,
                        merge_commit_message=f"异步自动合并需求 {requirement.requirement_code}: {requirement.title}"
                    )
                    
                    pr_info["merge_result"] = merge_result
                    merged_prs.append(pr_info)
                    
                    operation_results.append({
                        "step": "merge_pr",
                        "target_branch": target_branch,
                        "status": "success",
                        "pr_iid": pr_iid
                    })
                    
                    logger.info(f"[异步合并] PR合并成功: IID={pr_iid}")
                    
                except Exception as e:
                    error_msg = f"合并PR失败 (IID={pr_info.get('pr_iid')}): {str(e)}"
                    logger.error(f"[异步合并] {error_msg}")
                    operation_results.append({
                        "step": "merge_pr",
                        "target_branch": pr_info["target_branch"],
                        "status": "error",
                        "error": error_msg
                    })
                    raise Exception(error_msg)
            
            # 第三步：删除开发分支
            logger.info(f"[异步合并] 开始删除开发分支: {source_branch}")
            try:
                delete_success = gitlab_api.delete_branch(
                    project_id=project.id,
                    branch_name=source_branch
                )
                
                if delete_success:
                    operation_results.append({
                        "step": "delete_branch",
                        "branch": source_branch,
                        "status": "success"
                    })
                    logger.info(f"[异步合并] 开发分支删除成功: {source_branch}")
                else:
                    error_msg = f"删除开发分支失败: {source_branch}"
                    logger.error(f"[异步合并] {error_msg}")
                    operation_results.append({
                        "step": "delete_branch",
                        "branch": source_branch,
                        "status": "error",
                        "error": error_msg
                    })
                    raise Exception(error_msg)
                    
            except Exception as e:
                error_msg = f"删除开发分支失败: {str(e)}"
                logger.error(f"[异步合并] {error_msg}")
                operation_results.append({
                    "step": "delete_branch",
                    "branch": source_branch,
                    "status": "error",
                    "error": error_msg
                })
                raise Exception(error_msg)
            
            # 第四步：更新数据库状态
            MergeService._complete_merge_success(db, requirement, operator, project, source_branch, merged_prs)
            
        except Exception as e:
            logger.error(f"[异步合并] 合并流程失败: {str(e)}")
            MergeService._handle_merge_failure(db, requirement, operator, project, str(e))
            raise
    
    @staticmethod
    def _complete_merge_success(db: Session, requirement: RequirementModel, operator: User,
                              project: Project, source_branch: str, merged_prs: List[Dict[str, Any]]):
        """
        完成合并成功的处理
        """
        try:
            logger.info(f"[异步合并] 所有GitLab操作成功，开始更新数据库")
            
            # 更新需求状态
            requirement.status = RequirementStatus.COMPLETED
            requirement.update_time = get_utc_now()
            
            # 记录状态变更
            history = RequirementHistory(
                requirement_id=requirement.id,
                current_status_code=RequirementStatus.COMPLETED.name,
                action_code=RequirementAction.COMPLETE_MERGE.name,
                user_id=operator.id
            )
            db.add(history)
            
            # 保存所有PR信息到数据库
            for pr_info in merged_prs:
                pr_record = RequirementPullRequest(
                    requirement_id=requirement.id,
                    project_id=project.id,
                    source_branch=source_branch,
                    target_branch=pr_info["target_branch"],
                    pr_iid=pr_info["pr_iid"],
                    pr_url=pr_info["pr_url"],
                    status=PullRequestStatus.MERGED,
                    create_time=get_utc_now()
                )
                db.add(pr_record)
            
            # 提交数据库更改
            db.commit()
            logger.info(f"[异步合并] 数据库更新成功")
            
            # 发送成功通知
            MergeService._send_merge_notification(db, requirement, operator, project, "异步合并完成", "已完成", merged_prs)
            
        except Exception as e:
            db.rollback()
            logger.error(f"[异步合并] 更新数据库失败: {str(e)}")
            raise
    
    @staticmethod
    def _handle_merge_failure(db: Session, requirement: RequirementModel, operator: User, 
                            project: Project, error_message: str):
        """
        处理合并失败的情况
        """
        try:
            logger.info(f"[异步合并] 处理合并失败，回滚状态到验证中")
            
            # 回滚需求状态到验证中
            requirement.status = RequirementStatus.VALIDATING
            requirement.update_time = get_utc_now()
            
            # 记录失败历史
            history = RequirementHistory(
                requirement_id=requirement.id,
                current_status_code=RequirementStatus.VALIDATING.name,
                action_code=RequirementAction.REJECT_VALIDATION.name,
                user_id=operator.id,
                remark=f"异步合并失败: {error_message}"
            )
            db.add(history)
            
            db.commit()
            logger.info(f"[异步合并] 状态回滚成功")
            
            # 发送失败通知
            MergeService._send_merge_notification(db, requirement, operator, project, "异步合并失败", "验证中", None, error_message)
            
        except Exception as e:
            db.rollback()
            logger.error(f"[异步合并] 处理失败状态时出错: {str(e)}")
    
    @staticmethod
    def _send_merge_notification(db: Session, requirement: RequirementModel, operator: User,
                               project: Project, action: str, new_status: str, 
                               merged_prs: List[Dict[str, Any]] = None, error_message: str = None):
        """
        发送合并通知
        """
        try:
            # 构建通知消息
            if merged_prs:
                merged_branches = [pr["target_branch"] for pr in merged_prs]
                reason = f"已合并到分支: {', '.join(merged_branches)}"
            elif error_message:
                reason = f"失败原因: {error_message}"
            else:
                reason = None
            
            NotificationService.send_requirement_status_change_notification(
                db=db,
                requirement=requirement,
                operator=operator,
                project=project,
                action=action,
                new_status=new_status,
                reason=reason
            )
            
        except Exception as e:
            logger.error(f"[异步合并] 发送通知失败: {str(e)}")
